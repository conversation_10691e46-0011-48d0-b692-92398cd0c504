package hk.org.ha.sc3.sybasechatops.model.hachat;


import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
    
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class HachatCreateRoomReturn {
    private String roomId;
    
    public static HachatCreateRoomReturn fromJson(JsonNode json) {
        return HachatCreateRoomReturn.builder()
            .roomId(json.get("roomId").asText())
            .build();
     }
}
        

