package hk.org.ha.sc3.sybasechatops.model.hachat;


import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
    
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class HachatGetAccInfoReturn {
    private String status;
    private int accountId;
    
    public static HachatGetAccInfoReturn fromJson(JsonNode json) {

        return HachatGetAccInfoReturn.builder()
            .status(json.get("status").asText())
            .accountId(json.get("account").get("accountId").intValue())
            .build();
     }
}
        

