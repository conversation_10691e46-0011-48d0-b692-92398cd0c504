package hk.org.ha.sc3.sybasechatops.service;

import java.util.List;
import java.util.Optional;

import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import com.fasterxml.jackson.databind.JsonNode;

import hk.org.ha.sc3.sybasechatops.config.HachatConfig;
import hk.org.ha.sc3.sybasechatops.model.db.ChatopsUser;

import hk.org.ha.sc3.sybasechatops.model.hachat.HachatLoginReq;
import hk.org.ha.sc3.sybasechatops.model.hachat.HachatLoginReturn;
import hk.org.ha.sc3.sybasechatops.model.hachat.HachatGetAccInfoReq;
import hk.org.ha.sc3.sybasechatops.model.hachat.HachatViewRoomReq;
import hk.org.ha.sc3.sybasechatops.model.hachat.HachatCreateRoomReq;
import hk.org.ha.sc3.sybasechatops.model.hachat.HachatCreateRoomReturn;
import hk.org.ha.sc3.sybasechatops.model.hachat.HachatGetAccInfoReturn;
import hk.org.ha.sc3.sybasechatops.model.hachat.HachatAddMemberReq;
import hk.org.ha.sc3.sybasechatops.model.hachat.HachatReceiveMsgReq;
import hk.org.ha.sc3.sybasechatops.repository.ChatopsUserRepository;
import hk.org.ha.sc3.sybasechatops.repository.ChatroomRepository;

import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.netty.http.client.HttpClient;


@Slf4j
@Service
public class HachatService {
    private final HttpClient sslHttpClient;
    private final WebClient hachatClient;
    private final HachatConfig hachatConfig;
    private ChatopsUserRepository chatopsUserRepository;
    private ChatroomRepository chatroomRepository;

    public HachatService(HachatConfig hachatConfig, HttpClient sslHttpClient, ChatopsUserRepository chatopsUserRepository, ChatroomRepository chatroomRepository) {
        this.sslHttpClient = sslHttpClient;
        this.hachatConfig = hachatConfig;
        this.chatopsUserRepository = chatopsUserRepository;
        this.chatroomRepository = chatroomRepository;
        this.hachatClient = WebClient.builder()
                .baseUrl(hachatConfig.getBaseUrl())
                .defaultHeaders(headers -> headers.set("x-gateway-apikey", hachatConfig.getApiGatewayKey()))
                .clientConnector(new ReactorClientHttpConnector(this.sslHttpClient))
                .build();
    }

    public WebClient getHachatClient() {
        return this.hachatClient;
    }


    public List<String> getChatroomIdByTeam(String team) {
        List<String> chatroom = this.chatroomRepository.findChatroomsByTeam(team);
        log.info("Room list for [{}].", chatroom);
        return chatroom;
    }

    public List<String> getTeamsByCorpId(String corpId) {
        List<String> team = this.chatopsUserRepository.findTeamsByCorpId(corpId);
        log.info("Team for user [{}].", team);
        return team;
    }

    public List<String> getRoomListWithNullId() {
        List<String> roomList = this.chatroomRepository.findRoomsWithoutId();
        log.info("Room list for [{}].", roomList);
        return roomList;
    }

    public Mono<HachatLoginReturn> hachatLogin() {
        String path = "gateway/digitalwp-hachat-services/v3/service/user/pub/authentication/login";
        return getHachatClient().post()
                .uri(path)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(HachatLoginReq.builder()
                        .loginName(hachatConfig.getUsername())
                        .password(hachatConfig.getPassword())
                        .build())
                .retrieve()
                .bodyToMono(JsonNode.class)
                .map(HachatLoginReturn::fromJson);
    }

    public Mono<String> hachatLogout(String hachatApiKey, String userName) {
        String path = "gateway/digitalwp-hachat-services/v3/service/user/sec/authentication/logout";
        return getHachatClient().post()
                .uri(path)
                .header("apiKey", hachatApiKey)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(HachatLoginReq.builder().loginName(userName).build())
                .retrieve()
                .bodyToMono(String.class);
    }

    public Mono<HachatGetAccInfoReturn> getAccInfo(String hachatApiKey, String userName) {
        String path = "gateway/digitalwp-hachat-services/v3/service/user/sec/account/getaccountinfo";
        return getHachatClient().post()
                .uri(path)
                .header("apiKey", hachatApiKey)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(HachatGetAccInfoReq.builder().loginName(userName).build())
                .retrieve()
                .bodyToMono(JsonNode.class)
                .map(HachatGetAccInfoReturn::fromJson)
                .onErrorResume(e -> {
                    log.warn("Failed to get account info for user [{}]: {}", userName, e.getMessage());
                    return Mono.empty(); 
                });
    }

    public Mono<String> getAccProfile(String hachatApiKey, String userName) {
        String path = "gateway/digitalwp-hachat-services/v3/service/user/sec/account/getaccountprofile";
        return getHachatClient().post()
                .uri(path)
                .header("apiKey", hachatApiKey)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(HachatGetAccInfoReq.builder().loginName(userName).build())
                .retrieve()
                .bodyToMono(String.class);
    }

    
    public Mono<String> viewRoom(String hachatApiKey, int roomId) {
        String path = "gateway/digitalwp-hachat-services/v3/service/user/sec/room/viewroom";
        return getHachatClient().post()
                .uri(path)
                .header("apiKey", hachatApiKey)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(HachatViewRoomReq.builder().roomId(roomId).build())
                .retrieve()
                .bodyToMono(String.class);
    }

    public Mono<HachatCreateRoomReturn> createRoom(String hachatApiKey, int[] memberId, String roomName) {
        String path = "gateway/digitalwp-hachat-services/v3/service/user/sec/room/createmultipleroom";
        return getHachatClient().post()
                .uri(path)
                .header("apiKey", hachatApiKey)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(HachatCreateRoomReq.builder().memberIds(memberId).roomName(roomName).build())
                .exchangeToMono(response -> {
                    if (response.statusCode().is2xxSuccessful()) {
                        return response.bodyToMono(JsonNode.class)
                            .flatMap(json -> {
                                if (json.has("status") && "failed".equals(json.get("status").asText())) {
                                    String errorMsg = json.has("errMsgList") ?
                                        json.get("errMsgList").get(0).get("errCodes").get(0).get("msg").asText() :
                                        "Unknown error";
                                    log.error("Failed to create room [{}]. Error: {}", roomName, errorMsg);
                                    return Mono.empty();
                                }
                                return Mono.just(json);
                            });
                    }
                    return response.createException().flatMap(Mono::error);
                })
                .map(HachatCreateRoomReturn::fromJson);
    }

    public Mono<String> addRoomMembers(String hachatApiKey, int accountId, int roomId) {
        String path = "gateway/digitalwp-hachat-services/v3/service/user/sec/room/addroommembers";
        int[] accountIds= {accountId};
        return getHachatClient().post()
                .uri(path)
                .header("apiKey", hachatApiKey)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(HachatAddMemberReq.builder().accountIds(accountIds).roomId(roomId).build())
                .retrieve()
                .bodyToMono(String.class);
    }
    
    public Mono<String> receiveMsg(String hachatApiKey, int batchSize, int roomId, String upOrDown) {
        String path = "gateway/digitalwp-hachat-services/v3/service/user/sec/message/receiveaesmessages";
        return getHachatClient().post()
                .uri(path)
                .header("apiKey", hachatApiKey)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(HachatReceiveMsgReq.builder().batchSize(batchSize).roomId(roomId).upOrDown(upOrDown).build())
                .retrieve()
                .bodyToMono(String.class);
    }

    public Mono<String> updateRoomId(String roomName, String roomId) {
        return Mono.fromCallable(() -> {
            int rowsAffected = chatroomRepository.updateRoomId(roomName, roomId);
            if (rowsAffected == 0) {
                log.warn("No rows updated for teamName: {}", roomName);
                return "No rows updated for room: " + roomName;
            } else {
                log.info("Updated team_id to {} for team: {}", roomId, roomName);
                return "Updated";
            }
        });
    }


    public Flux<String> createAndUpdateRoom(String hachatApiKey) {
        int[] memberIds = {102663, 103695};
        List<String> roomList = getRoomListWithNullId();
        Flux<String> roomListFlux = Flux.fromIterable(roomList).cache();
        Flux<String> resultFlux = roomListFlux.concatMap(roomName -> {
            Mono<HachatCreateRoomReturn> createRoomMono = createRoom(hachatApiKey, memberIds, roomName);
            return createRoomMono
                .filter(createResponse -> createResponse.getRoomId() != null)
                .flatMap(createResponse -> 
                    updateRoomId(roomName, createResponse.getRoomId())
                       .doOnSuccess(updateCount -> log.info("Room processed for: {} with ID: {}", roomName, createResponse.getRoomId())))
                .onErrorResume(e -> Mono.empty()); 
        });
        return resultFlux;
    }


    public Flux<String> addUserToAllChatrooms(String hachatApiKey) {
        List<ChatopsUser> chatopsUsers = chatopsUserRepository.findAll();
    
        return Flux.fromIterable(chatopsUsers)
            .cache()
            .flatMap(user ->
                getAccInfo(hachatApiKey, user.getCorpId())
                    .map(HachatGetAccInfoReturn::getAccountId)
                    .flatMapMany(accountId ->
                        Flux.fromIterable(getChatroomIdByTeam(user.getTeam()))
                            .flatMap(roomId ->
                                addRoomMembers(hachatApiKey, accountId, Integer.parseInt(roomId))
                            )
                    )
            );
    }

}