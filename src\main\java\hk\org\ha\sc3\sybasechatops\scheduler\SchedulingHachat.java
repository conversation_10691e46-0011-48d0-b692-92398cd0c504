package hk.org.ha.sc3.sybasechatops.scheduler;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import hk.org.ha.sc3.sybasechatops.config.SchedulerConfig;
import hk.org.ha.sc3.sybasechatops.model.hachat.HachatLoginReturn;
import hk.org.ha.sc3.sybasechatops.service.HachatService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class SchedulingHachat {
    private final HachatService hachatService;
    private final SchedulerConfig schedulerConfig;
    //private String apiToken;

    public SchedulingHachat(HachatService hachatService, SchedulerConfig schedulerConfig) {
        this.hachatService = hachatService;
        this.schedulerConfig = schedulerConfig;
    }


    @Scheduled(cron = "0 0 23 * * *")
    public void schedulingCreateTeamFromMySQL() {
        if (!schedulerConfig.isHachatEnabled()) {
            log.info("Team scheduling is disabled. Skipping scheduled task execution.");
            return;
        }
        
        log.info("Starting scheduled HaChat team creation and add member");

        HachatLoginReturn loginResult = hachatService.hachatLogin().block();
        if (loginResult == null || !"success".equals(loginResult.getStatus())) {
            log.error("Unable to login and retrieve API token. Skipping task.");
            return;
        }
    
        String apiToken = loginResult.getApiKey();
        log.info("API token: {}", apiToken);

        String createTeamResult = hachatService.createAndUpdateRoom(apiToken)
                .blockLast();
        log.info("Create Team Result: {}", createTeamResult);

        String assignTeamResult = hachatService.addUserToAllChatrooms(apiToken)
                .blockLast();
        log.info("Assign Team Result: {}", assignTeamResult);
   
        
        log.info("Completed scheduled HaChat team creation and add member");
    }


}
