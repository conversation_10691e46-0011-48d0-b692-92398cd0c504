package hk.org.ha.sc3.sybasechatops.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import org.springframework.transaction.annotation.Transactional;

import hk.org.ha.sc3.sybasechatops.model.db.ChatopsUser;

public interface ChatopsUserRepository extends JpaRepository<ChatopsUser, String> {
    List<ChatopsUser> findByTeam(String teamname);

    @Query("SELECT DISTINCT corpId FROM ChatopsUser")
    List<String> findAllCorpId();

    @Query("SELECT team FROM ChatopsUser WHERE corp_id = :corpId")
    List<String> findTeamsByCorpId(String corpId);

    @Query("SELECT DISTINCT team FROM ChatopsUser")
    List<String> findDistinctTeam();

    @Query("SELECT Distinct team FROM ChatopsUser WHERE teamId IS Null OR teamId = 0")
    List<String> findTeamsWithoutId();

    @Modifying
    @Transactional
    @Query("UPDATE ChatopsUser SET teamId = :teamId WHERE team = :teamName")
    int updateTeamIdByTeam(String teamName, Integer teamId);

    Optional<ChatopsUser> findByCorpId(String corpId);

    List<ChatopsUser> findAll();
}