package hk.org.ha.sc3.sybasechatops.model.db;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "chatops_user", schema = "health_check", catalog = "health_check")
@Getter
@Setter
public class ChatopsUser{
    @Id
    private String corpId;
    
    private String team;

    // The team ID is from  Ansible Automation Platform
    private Integer teamId;
}