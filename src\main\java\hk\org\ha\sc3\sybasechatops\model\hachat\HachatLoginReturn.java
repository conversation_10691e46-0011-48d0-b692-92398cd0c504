package hk.org.ha.sc3.sybasechatops.model.hachat;


import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
    
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class HachatLoginReturn {
    private String status;
    private String apiKey;
    
    public static HachatLoginReturn fromJson(JsonNode json) {

        return HachatLoginReturn.builder()
            .status(json.get("status").asText())
            .apiKey(json.has("apiKey") ? json.get("apiKey").asText() : null)
            .build();
     }
}
        

