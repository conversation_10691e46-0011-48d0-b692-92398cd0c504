package hk.org.ha.sc3.sybasechatops.service;

import java.awt.Color;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.imageio.ImageIO;

import org.springframework.stereotype.Service;

import com.lowagie.text.Chunk;
import com.lowagie.text.Document;
import com.lowagie.text.DocumentException;
import com.lowagie.text.Font;
import com.lowagie.text.FontFactory;
import com.lowagie.text.Image;
import com.lowagie.text.Paragraph;
import com.lowagie.text.Phrase;
import com.lowagie.text.Rectangle;
import com.lowagie.text.pdf.PdfPCell;
import com.lowagie.text.pdf.PdfPTable;
import com.lowagie.text.pdf.PdfWriter;

import hk.org.ha.sc3.sybasechatops.config.PdfConfig;
import hk.org.ha.sc3.sybasechatops.helper.AnsiColorConverter;
import hk.org.ha.sc3.sybasechatops.model.PdfContent;
import hk.org.ha.sc3.sybasechatops.model.db.CommandResult;
import hk.org.ha.sc3.sybasechatops.model.db.Database;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class PDFService {
    private final static String titleFormat = "%-20s: %-20s";
    private final static int fontSize = 16;
    private final static Font fontBody = FontFactory.getFont(FontFactory.COURIER, fontSize, Color.WHITE);
    private final static Font fontTitle = FontFactory.getFont(FontFactory.COURIER_BOLD, 20, Color.WHITE);
    // private final static float pdfMinWidth = 600f;
    private final static float pdfMaxWidth = 1920f;
    private final static float pdfMaxHeight = 1080f;
    private final static float margin = 36f;
    private final static float padding = 16f;
    // private final static float fontBodyWidthPoint =
    // PDFService.fontBody.getBaseFont().getWidthPoint("w", fontSize);

    private PdfConfig pdfConfig;

    /* A constructor to accept all private fields */
    public PDFService(PdfConfig pdfConfig) {
        this.pdfConfig = pdfConfig;
    }

    public String generateBase64FromPdf(String fileName) throws IOException {
        String outputPath = getFileAbsolutePath(fileName);
        log.info("Coverting PDF {} to Base64", outputPath);
        File file = new File(outputPath);
        FileInputStream inputStream = new FileInputStream(file);
        byte[] bytes = new byte[(int) file.length()];
        inputStream.read(bytes);
        inputStream.close();
        String base64 = Base64.getEncoder().encodeToString(bytes);
        return base64;
    }

    private void addRequestInfoHeader(Document document, String requester, LocalDateTime dateTime) {
        Paragraph requestInfoTitle = new Paragraph("Request Info:", fontTitle);
        Paragraph requesterP = new Paragraph(String.format(titleFormat, "Requester", requester), fontBody);
        Paragraph requestTimeP = new Paragraph(String.format(titleFormat, "Request Time", dateTime),
                fontBody);
        requestTimeP.setSpacingAfter(margin);
        document.add(requestInfoTitle);
        document.add(requesterP);
        document.add(requestTimeP);
    }

    private void addHealthCheckHeader(Document document, PdfContent pdfContent, CommandResult result) {
        Paragraph hcInfo = new Paragraph(String.format("%s:", "Health Check Info"), fontTitle);
        Paragraph hostNameP = new Paragraph(
                String.format(titleFormat, "Host Name", pdfContent.getHostName()),
                fontBody);
        Paragraph instanceNameP = new Paragraph(
                String.format(titleFormat, "Instance Name", pdfContent.getInstanceName()),
                fontBody);
        Paragraph commandP = new Paragraph(
                String.format(titleFormat, "Command", result.getCommand()),
                fontBody);
        Paragraph requestElapseP = new Paragraph(
                String.format(titleFormat, "Execution Time", result.getElapsedTime() + "s"),
                fontBody);
        requestElapseP.setSpacingAfter(margin);

        document.add(hcInfo);
        document.add(hostNameP);
        document.add(instanceNameP);
        document.add(commandP);
        document.add(requestElapseP);

        Paragraph title = new Paragraph(String.format("%s:", result.getPdfTitle()), fontTitle);
        title.setSpacingAfter(30);
        document.add(title);

    }

    public void generateDbHealthCheckPdf(String fileName, String requester, LocalDateTime dateTime,
            List<PdfContent> pdfContents) {
        String outputPath = getFileAbsolutePath(fileName);

        Rectangle customPageSize = new Rectangle(pdfMaxWidth, pdfMaxHeight);
        customPageSize.setBackgroundColor(Color.BLACK);
        try (Document document = new Document(customPageSize)) {
            PdfWriter writer = PdfWriter.getInstance(document, new FileOutputStream(outputPath));
            /*
             * Disabled dynamic width and height as mobile app previous look does not
             * support different width and height size in one PDF
             */
            /* 10width for table border buffer */
            // float dynamicWidth = result.getMaxLineLength() * fontBodyWidthPoint + margin
            // * 2 + padding * 2
            // + 10;
            // dynamicWidth = dynamicWidth > pdfMaxWidth ? pdfMaxWidth
            // : dynamicWidth < pdfMinWidth ? pdfMinWidth : dynamicWidth;

            // float dynamicHeight = result.getLineNum() * fontSize + margin * 2 + 200;
            document.open();
            document.setMargins(margin, margin, margin, margin);

            boolean shouldAddHeader = true;

            for (PdfContent pdfContent : pdfContents) {
                /* for loop hashmap */
                for (CommandResult result : pdfContent.getHealthCheckContent()) {

                    if (shouldAddHeader) {
                        this.addRequestInfoHeader(document, requester, dateTime);
                        shouldAddHeader = false;
                    }

                    this.addHealthCheckHeader(document, pdfContent, result);

                    switch (result.getReturnType()) {
                        case SSH:
                            if (result.getRc() > 0 || !result.getStderr().isEmpty()) {
                                PdfPTable errTable = new PdfPTable(1);
                                PdfPCell rc = new PdfPCell(
                                        new Phrase(String.format("The exit code is %s", result.getRc()), fontBody));
                                PdfPCell stderr = new PdfPCell(new Phrase(result.getStderr(), fontBody));
                                rc.setBorderColor(Color.RED);
                                rc.setPadding(padding);
                                stderr.setBorderColor(Color.RED);
                                stderr.setPadding(padding);

                                errTable.addCell(rc);
                                errTable.addCell(stderr);
                                errTable.setSpacingAfter(30);
                                errTable.setWidthPercentage(100);
                                document.add(errTable);
                            }

                            PdfPTable table = new PdfPTable(1);
                            table.setSplitLate(false);
                            table.setWidthPercentage(100);
                            PdfPCell stdout = new PdfPCell(convertAnsiColorString(result.getStdout()));
                            stdout.setPadding(padding);
                            stdout.cloneNonPositionParameters(stdout);
                            stdout.setBorderColor(Color.WHITE);

                            table.addCell(stdout);
                            document.add(table);
                            break;
                        case IMAGE:
                            int remainingHeight = getRemainingPageHeight(document, writer);

                            String origImagePath = result.getFile();
                            for (String imageStr : this.cropScreenshot(remainingHeight, origImagePath)) {
                                Image image = Image.getInstance(imageStr);
                                document.add(image);
                                document.newPage();
                            }
                        default:
                            break;
                    }

                    document.newPage();
                }
            }
        } catch (DocumentException | IOException e) {
            log.error("pdf exception", e);
        }

    }

    public void generateInstanceList(String fileName, String requester, LocalDateTime dateTime,
            List<Database> databases) {
        String outputPath = getFileAbsolutePath(fileName);
        Document document = new Document();

        try {
            // step 2:
            // we create a writer that listens to the document
            // and directs a PDF-stream to a file
            PdfWriter.getInstance(document, new FileOutputStream(outputPath));

            // step 3: we open the document
            document.open();

            Paragraph requesterP = new Paragraph(String.format(titleFormat, "Requester", requester), fontBody);
            Paragraph requestTimeP = new Paragraph(String.format(titleFormat, "Request Time", dateTime), fontBody);
            requestTimeP.setSpacingAfter(30);
            document.add(requesterP);
            document.add(requestTimeP);

            PdfPTable table = new PdfPTable(2);
            table.addCell("Host");
            table.addCell("Instance");
            for (Database database : databases) {
                table.addCell(database.getId().getHost());
                table.addCell(database.getId().getInstance());
            }

            document.add(table);
        } catch (DocumentException | IOException de) {
            System.err.println(de.getMessage());
        } finally {
            // step 5: we close the document
            document.close();
        }
    }

    private String getFileAbsolutePath(String fileName) {
        return String.format("%s/%s", pdfConfig.getStorage(), fileName);
    }

    /* Convert ansi code string to openpdf paragraph */
    private Paragraph convertAnsiColorString(String ansiColorString) {
        Paragraph paragraph = new Paragraph();
        String patternStr = "\\u001B\\[([\\d;]*)m";
        Pattern ansiPattern = Pattern.compile(patternStr);
        if (ansiColorString == null){
            return paragraph;
        }
        String[] chunks = ansiColorString.split(patternStr);
        Matcher matcher = ansiPattern.matcher(ansiColorString);

        Chunk pdfFirstChunk = new Chunk(chunks[0], fontBody);
        paragraph.add(pdfFirstChunk);

        int i = 1;
        while (matcher.find()) {
            String ansiColorCode = matcher.group(1);
            Font colorFont = FontFactory.getFont(FontFactory.COURIER, fontSize, Color.WHITE);
            colorFont.setColor(AnsiColorConverter.convertAnsiColor(ansiColorCode));
            Chunk pdfChunk = new Chunk(chunks[i++]);
            pdfChunk.setFont(colorFont);
            paragraph.add(pdfChunk);
        }

        return paragraph;
    }

    private ArrayList<String> cropScreenshot(int firstCropHeight, String screenshotPath) throws IOException {
        ArrayList<String> files = new ArrayList<>();
        BufferedImage originalImage = ImageIO.read(new File(screenshotPath));

        // Define the dimensions of the cropped images
        int cropWidth = 1920;
        int cropHeight = 1000;

        // Crop the original image into multiple smaller images
        int y = 0;
        boolean isFirstCrop = true;

        while (y < originalImage.getHeight()) {
            for (int x = 0; x < originalImage.getWidth(); x += cropWidth) {
                int croppedWidth = Math.min(cropWidth, originalImage.getWidth() - x);
                int croppedHeight;

                if (isFirstCrop) {
                    croppedHeight = Math.min(firstCropHeight, originalImage.getHeight() - y);
                } else {
                    croppedHeight = Math.min(cropHeight, originalImage.getHeight() - y);
                }

                // Crop the image
                BufferedImage croppedImage = originalImage.getSubimage(x, y, croppedWidth, croppedHeight);

                String fileName = (new SimpleDateFormat("yyyy_MM_dd_HH_mm_ss_SSS")).format(new Date()) + x + "_" + y
                        + ".png";
                // Save the cropped image
                File outputfile = new File("./screenshot", fileName);
                files.add(outputfile.getAbsolutePath());
                ImageIO.write(croppedImage, "png", outputfile);
            }

            if (isFirstCrop) {
                y += firstCropHeight;
                isFirstCrop = false;
            } else {
                y += cropHeight;
            }
        }
        return files;
    }

    private int getRemainingPageHeight(Document document, PdfWriter writer) {
        float currentY = writer.getVerticalPosition(true);
        float bottomMargin = document.bottomMargin();
        return (int) (currentY - bottomMargin);
    }
}
