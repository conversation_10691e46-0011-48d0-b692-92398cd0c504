package hk.org.ha.sc3.sybasechatops.repository;

import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import hk.org.ha.sc3.sybasechatops.model.db.ChatopsChatroom;

@Repository
public interface ChatroomRepository extends JpaRepository<ChatopsChatroom, String> {
    boolean existsByToken(String token);
    Optional<ChatopsChatroom> findByTokenAndRoomId(String token, String roomId);
    Optional<ChatopsChatroom> findByRoomId(String roomId);

    @Query("SELECT DISTINCT roomId FROM ChatopsChatroom WHERE team = :team")
    List<String> findChatroomsByTeam(String team);

    @Query("SELECT Distinct roomName FROM ChatopsChatroom WHERE roomId IS Null OR roomId = 0")
    List<String> findRoomsWithoutId();

    @Modifying
    @Transactional
    @Query("UPDATE ChatopsChatroom SET roomId = :roomId WHERE roomName = :roomName")
    int updateRoomId(String roomName, String roomId);

}